import {
  IsString,
  IsArray,
  IsNumber,
  IsBoolean,
  IsEnum,
  IsOptional,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

export class ProductVariantDto {
  @IsString()
  id: string;

  @IsEnum(['small', 'medium', 'large', 'extra-large'])
  size: string;

  @IsEnum(['light', 'medium', 'heavy', 'premium'])
  bedazzlingLevel: string;

  @IsEnum(['basic', 'premium', 'luxury'])
  frameOption: string;

  @IsOptional()
  @IsEnum(['simple', 'detailed', 'intricate'])
  complexity?: string;

  @IsNumber()
  @Min(0)
  price: number;

  @IsNumber()
  @Min(0)
  stock: number;
}

export class CreateProductDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsEnum(['pre-made', 'custom'])
  category: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  images?: string[];

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ProductVariantDto)
  variants: ProductVariantDto[];

  @IsNumber()
  @Min(0)
  basePrice: number;

  @IsOptional()
  @IsBoolean()
  featured?: boolean;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsString()
  metaTitle?: string;

  @IsOptional()
  @IsString()
  metaDescription?: string;
}
