import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class FormDataTransformInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: <PERSON>Hand<PERSON>): Observable<any> {
    const request = context.switchToHttp().getRequest();
    
    if (request.body) {
      // Transform variants from JSON string to array
      if (request.body.variants && typeof request.body.variants === 'string') {
        try {
          request.body.variants = JSON.parse(request.body.variants);
        } catch (error) {
          request.body.variants = [];
        }
      }

      // Transform tags from JSON string to array
      if (request.body.tags && typeof request.body.tags === 'string') {
        try {
          request.body.tags = JSON.parse(request.body.tags);
        } catch (error) {
          request.body.tags = [];
        }
      }

      // Transform basePrice to number
      if (request.body.basePrice && typeof request.body.basePrice === 'string') {
        request.body.basePrice = parseFloat(request.body.basePrice);
      }

      // Transform featured to boolean
      if (request.body.featured && typeof request.body.featured === 'string') {
        request.body.featured = request.body.featured === 'true';
      }

      // Transform variant properties to correct types
      if (Array.isArray(request.body.variants)) {
        request.body.variants = request.body.variants.map((variant: any) => ({
          ...variant,
          price: typeof variant.price === 'string' ? parseFloat(variant.price) : variant.price,
          stock: typeof variant.stock === 'string' ? parseInt(variant.stock, 10) : variant.stock,
        }));
      }
    }

    return next.handle();
  }
}
